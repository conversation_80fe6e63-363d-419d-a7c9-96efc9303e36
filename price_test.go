package omnichannel_product_list

import (
	"testing"
)

func TestPullPriceInfo(t *testing.T) {
	token := "c-he2WuDb54GslJ6XKCgHBxKuKReXo7fpUyh3lLpQVQ.ACtpl_mp5YmsMFWmFfZuyh57lPkSOX2_2x6FNVnT_Es"
	storeId := uint64(4972806615293067264)
	//storeId := uint64(4937396346408763392)
	host := "https://hipos-saas-qa.hexcloud.cn"
	//host := "http://127.0.0.1:8081"
	dir := "/Users/<USER>/go/src/codeup.aliyun.com/619e3e4fcb55679b040c45b4/hicloud/omnichannel-product-list"
	withSaveFunc := WithSaveFunc(DefaultSaveFunc(dir))
	withGetFunc := WithGetFunc(DefaultGetFunc(dir))
	_, err := PullPriceInfo(host, token, storeId, "zh", withSaveFunc, withGetFunc)
	if err != nil {
		t.Errorf("PullPriceInfo error: %v", err)
	}
}

// BenchmarkGetMenuData 基准测试GetMenuData函数性能
func BenchmarkGetMenuData(b *testing.B) {
	// 模拟测试数据
	host := "https://hipos-saas-qa.hexcloud.cn"
	token := "test_token"
	storeID := uint64(123456)
	lang := "zh"
	channelCode := "POS"

	// 使用内存中的测试数据
	testDir := "./testdata"
	options := []PullPriceOption{
		WithGetFunc(DefaultGetFunc(testDir)),
		WithSaveFunc(DefaultSaveFunc(testDir)),
		WithMaxRetries(1), // 减少重试次数以加快测试
		WithRetryDelay(100 * time.Millisecond),
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_, err := GetMenuData(host, token, storeID, lang, channelCode, options...)
		if err != nil {
			// 在基准测试中，网络错误是预期的，只记录但不失败
			b.Logf("GetMenuData error (expected in benchmark): %v", err)
		}
	}
}
