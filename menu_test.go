package omnichannel_product_list

import (
	"context"
	"log"
	"os"
	"testing"
)

func TestGetMenuData(t *testing.T) {
	token := "meOjy-40ZsQuUm_4ztsGZX_AxhTaWgCW9WbYtFUckrg.dqGjYFz-96ARQvKBOoHsiqQ0eleaE6Ru92JAUnSnrbk"
	storeId := uint64(4972806615293067264)
	//storeId := uint64(4937396346408763392)
	host := "https://hipos-saas-qa.hexcloud.cn"
	//host := "http://127.0.0.1:8081"
	dir := "/Users/<USER>/go/src/codeup.aliyun.com/619e3e4fcb55679b040c45b4/hicloud/omnichannel-product-list"
	menuData, err := GetMenuData(context.Background(), host, token, &GetMenuParams{ChannelCode: "POS", StoreID: storeId, Lang: "zh-CN"}, withSaveFunc, withGetFunc)
	if err != nil {
		t.Errorf("PullBaseData error: %v", err)
	}
	if err := os.WriteFile("menu.json", menuData, 0666); err != nil {
		log.Fatal(err)
	}
}
